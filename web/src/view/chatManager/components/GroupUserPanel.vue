<template>
  <div class="group-user-panel">
    <div class="panel-header">
      <h4>群成员 ({{ members.length }})</h4>
    </div>
    
    <div class="member-list">
      <el-scrollbar class="member-scrollbar">
        <div class="member-item" v-for="member in members" :key="member.id">
          <div class="member-info" @click="handleMemberClick(member)">
            <div class="avatar-wrapper">
              <el-avatar :size="32" :src="member.avatar">
                <el-icon><User /></el-icon>
              </el-avatar>
              <!-- <div 
                v-if="member.online" 
                class="online-dot"
              ></div> -->
            </div>
            
            <div class="member-details">
              <div class="member-name">
                <span class="nickname-text">{{ member.nickname }}</span>
                <!-- <el-tag v-if="member.isAdmin" size="small" type="warning">管理员</el-tag> -->
              </div>
              <!-- <div class="member-status">
                <span :class="member.online ? 'online' : 'offline'">
                  {{ member.online ? '在线' : '离线' }}
                </span>
              </div> -->
            </div>
          </div>
          
          <el-dropdown 
            @command="handleCommand"
            trigger="click"
            class="member-actions"
          >
            <el-button type="text" size="small">
              <el-icon><MoreFilled /></el-icon>
            </el-button>
            <template #dropdown>
              <el-dropdown-menu>
                <el-dropdown-item :command="`chat_${member.id}`">
                  <el-icon><ChatDotRound /></el-icon>
                  私聊
                </el-dropdown-item>
                <el-dropdown-item :command="`profile_${member.id}`">
                  <el-icon><User /></el-icon>
                  查看资料
                </el-dropdown-item>
                <!-- <el-dropdown-item 
                  v-if="canManage && !member.isAdmin" 
                  :command="`admin_${member.id}`"
                  divided
                >
                  <el-icon><Star /></el-icon>
                  设为管理员
                </el-dropdown-item> -->
                <!-- <el-dropdown-item 
                  v-if="canManage" 
                  :command="`kick_${member.id}`"
                  class="danger-item"
                >
                  <el-icon><Remove /></el-icon>
                  移出群聊
                </el-dropdown-item> -->
              </el-dropdown-menu>
            </template>
          </el-dropdown>
        </div>
      </el-scrollbar>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { ElMessage, ElMessageBox } from 'element-plus'
import { getImUserList } from '@/api/im/imuser'

defineOptions({
  name: 'GroupUserPanel'
})

const props = defineProps({
  group: {
    type: Object,
    required: true
  }
})

const emit = defineEmits(['member-click', 'start-chat'])

// 群成员数据
const members = ref([])
const loading = ref(false)

// 获取群成员信息
const getGroupMembers = async () => {
  if (!props.group || !props.group.FromID || !Array.isArray(props.group.FromID)) {
    console.warn('群组信息不完整或FromID不是数组')
    return
  }

  loading.value = true
  try {
    // 调用用户列表接口获取所有用户
    const response = await getImUserList({
      page: 1,
      pageSize: 1000 // 获取足够多的用户数据
    })

    if (response.code === 0 && response.data && response.data.list) {
      const allUsers = response.data.list
      const groupMemberIds = props.group.FromID

      // 筛选出群成员
      const groupMembers = allUsers.filter(user =>
        groupMemberIds.includes(user.id)
      ).map(user => ({
        id: user.id,
        nickname: user.name || user.iphoneNum || `用户${user.id}`,
        avatar: user.headImg || '',
        online: user.online || false,
        isAdmin: props.group.Admins && props.group.Admins.includes(user.id),
        isOwner: props.group.GroupHave === user.id,
        phone: user.iphoneNum,
        lastLoginTime: user.lastLoginTime,
        joinTime: user.createdAt,
        userConfig: user.userConfig
      }))

      // 按照群主、管理员、普通成员的顺序排序
      groupMembers.sort((a, b) => {
        if (a.isOwner && !b.isOwner) return -1
        if (!a.isOwner && b.isOwner) return 1
        if (a.isAdmin && !b.isAdmin) return -1
        if (!a.isAdmin && b.isAdmin) return 1
        return 0
      })

      members.value = groupMembers
      console.log('群成员数据:', groupMembers)
    } else {
      console.error('获取用户列表失败:', response)
      ElMessage.error('获取群成员信息失败')
    }
  } catch (error) {
    console.error('获取群成员信息出错:', error)
    ElMessage.error('获取群成员信息出错')
  } finally {
    loading.value = false
  }
}

// 当前用户是否有管理权限
const canManage = computed(() => {
  // 这里应该根据实际的权限逻辑判断
  return true
})

// 监听群组变化，重新获取成员信息
watch(() => props.group, (newGroup) => {
  if (newGroup && newGroup.FromID) {
    getGroupMembers()
  }
}, { immediate: true, deep: true })

// 组件挂载时获取成员信息
onMounted(() => {
  if (props.group && props.group.FromID) {
    getGroupMembers()
  }
})

// 处理成员点击
const handleMemberClick = (member) => {
  emit('member-click', member)
}

// 处理下拉菜单命令
const handleCommand = (command) => {
  const [action, userId] = command.split('_')
  const member = members.value.find(m => m.id === userId)
  
  if (!member) return
  
  switch (action) {
    case 'chat':
      startPrivateChat(member)
      break
    case 'profile':
      showMemberProfile(member)
      break
  }
}

// 开始私聊
const startPrivateChat = (member) => {
  emit('start-chat', member)
  ElMessage.success(`开始与 ${member.nickname} 的私聊`)
}

// 查看成员资料
const showMemberProfile = (member) => {
  ElMessage.info(`查看 ${member.nickname} 的资料`)
  // 这里应该打开用户资料弹窗
}

</script>

<style lang="scss" scoped>
.group-user-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: #374151;
  color: #f3f4f6;
}

.panel-header {
  padding: 16px 20px;
  border-bottom: 1px solid #6b7280;

  h4 {
    margin: 0;
    font-size: 14px;
    font-weight: 500;
    color: #f3f4f6;
  }
}

.member-list {
  flex: 1;
  overflow: hidden;

  .member-scrollbar {
    height: 100%;
  }
}

.member-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 10px 20px;
  transition: background-color 0.2s;

  &:hover {
    background: rgba(75, 85, 99, 0.6);
  }

  .member-info {
    display: flex;
    align-items: center;
    gap: 12px;
    flex: 1;
    cursor: pointer;

    .avatar-wrapper {
      position: relative;

      .online-dot {
        position: absolute;
        bottom: 0;
        right: 0;
        width: 10px;
        height: 10px;
        background: #22c55e;
        border: 2px solid #374151;
        border-radius: 50%;
      }
    }

    .member-details {
      flex: 1;
      min-width: 0;

      .member-name {
        display: flex;
        align-items: center;
        gap: 6px;
        font-size: 14px;
        font-weight: 500;
        color: #f3f4f6;
        margin-bottom: 2px;
        width: 100%;

        .nickname-text {
          flex: 1;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
          min-width: 0;
        }

        .el-tag {
          font-size: 10px;
          height: 16px;
          line-height: 14px;
          flex-shrink: 0;
        }
      }

      .member-status {
        font-size: 12px;

        .online {
          color: #22c55e;
        }

        .offline {
          color: #9ca3af;
        }
      }
    }
  }

  .member-actions {
    .el-button {
      color: #9ca3af;

      &:hover {
        color: #f3f4f6;
      }
    }
  }
}

:deep(.el-dropdown-menu__item) {
  &.danger-item {
    color: #e74c3c;
    
    &:hover {
      background: #e74c3c;
      color: white;
    }
  }
}
</style>
