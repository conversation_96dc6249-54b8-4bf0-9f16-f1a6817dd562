<template>
  <el-dialog
    v-model="visible"
    title=""
    :width="1200"
    :height="750"
    :show-close="false"
    :close-on-click-modal="false"
    :close-on-press-escape="false"
    class="chat-dialog"
    destroy-on-close
  >
    <template #header>
      <div class="dialog-header flex justify-between items-center">
        <div class="header-left">
        </div>
        <div class="header-right">
          <el-button @click="closeDialog" type="danger" size="small" circle>
            <el-icon><Close /></el-icon>
          </el-button>
        </div>
      </div>
    </template>

    <div class="chat-container flex h-full bg-gray-800">
      <!-- 左侧会话列表 -->
      <div class="conversation-sidebar w-[350px] bg-gray-900 border-r border-gray-700 flex flex-row">
        <div class="sidebar-tabs w-16 flex flex-col bg-gray-950 flex-shrink-0">
          <div
            class="tab-item flex flex-col items-center justify-center gap-1 py-4 px-2 text-gray-400 cursor-pointer transition-all duration-200 hover:bg-gray-800 hover:text-white"
            :class="{ 'bg-green-600 text-white': activeTab === 'groups' }"
            @click="activeTab = 'groups'"
          >
            <el-icon class="text-lg"><ChatLineRound /></el-icon>
            <span class="text-xs font-medium text-center">群聊</span>
          </div>
          <div
            class="tab-item flex flex-col items-center justify-center gap-1 py-4 px-2 text-gray-400 cursor-pointer transition-all duration-200 hover:bg-gray-800 hover:text-white"
            :class="{ 'bg-green-600 text-white': activeTab === 'friends' }"
            @click="activeTab = 'friends'"
          >
            <el-icon class="text-lg"><User /></el-icon>
            <span class="text-xs font-medium text-center">好友</span>
          </div>
        </div>

        <div class="conversation-list flex-1 overflow-y-auto bg-gray-900">
          <!-- 群组会话 -->
          <div v-if="activeTab === 'groups'" class="conversation-section p-2 h-full">
            <div v-if="groupConversations.length === 0" class="empty-state text-center text-gray-400 py-8">
              <p>正在加载群组列表...</p>
              <p class="text-xs mt-2">群组数量: {{ groupConversations.length }}</p>
            </div>
            
            <ConversationCard
              v-for="group in groupConversations"
              :key="group.id"
              :conversation="group"
              :is-selected="selectedConversation?.id === group.id"
              @click="selectConversation(group)"
            />
          </div>

          <!-- 好友会话 -->
          <div v-if="activeTab === 'friends'" class="conversation-section p-2 h-full">
            <ConversationCard
              v-for="friend in friendConversations"
              :key="friend.id"
              :conversation="friend"
              :is-selected="selectedConversation?.id === friend.id"
              @click="selectConversation(friend)"
            />
          </div>
        </div>
      </div>

      <!-- 右侧聊天区域 -->
      <div class="chat-main flex-1 flex flex-col bg-gray-700">
        <MessagePanel
          v-if="selectedConversation"
          :conversation="selectedConversation"
        />
        <div v-else class="empty-chat flex items-center justify-center h-full rounded-lg">
          <el-empty description="请选择一个会话开始聊天" />
        </div>
      </div>

      <!-- 群组成员面板 -->
      <div v-if="selectedConversation?.type === 'group'" class="group-members bg-gray-800 border-l border-gray-600">
        <GroupUserPanel :group="selectedConversation?.originalData" />
      </div>
    </div>
  </el-dialog>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import ConversationCard from './components/ConversationCard.vue'
import MessagePanel from './components/MessagePanel.vue'
import GroupUserPanel from './components/GroupUserPanel.vue'
import { getGroupList } from '@/api/chat.js'

defineOptions({
  name: 'ChatDialog'
})

// Props
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  user: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits(['update:modelValue', 'close'])

// 响应式数据
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const currentUser = ref(props.user)
const activeTab = ref('groups')
const selectedConversation = ref(null)

// 群组会话数据
const groupConversations = ref([])

const friendConversations = ref([
  {
    id: 'friend_1',
    type: 'friend',
    name: '张三',
    avatar: '',
    lastMessage: '你好',
    lastTime: '10:30',
    unread: 2,
    online: true
  },
  {
    id: 'friend_2',
    type: 'friend',
    name: '李四',
    avatar: '',
    lastMessage: '在吗？',
    lastTime: '09:15',
    unread: 23,
    online: false
  },
  {
    id: 'friend_3',
    type: 'friend',
    name: '王五',
    avatar: '',
    lastMessage: '明天见',
    lastTime: '昨天',
    unread: 1,
    online: true
  }
])

// 方法
const selectConversation = (conversation) => {
  selectedConversation.value = conversation
}

const closeDialog = () => {
  visible.value = false
  emit('close')
}

// 获取群组列表
const fetchGroupList = async () => {
  try {
    console.log('开始获取群组列表...')
    const response = await getGroupList({})
    console.log('API 完整响应:', response)

    // 检查响应数据结构
    const responseData = response.data || response
    console.log('响应数据:', responseData)

    if (responseData.code === 0 && responseData.data) {
      console.log('群组数据:', responseData.data)
      // 将 API 返回的数据转换为组件需要的格式
      groupConversations.value = responseData.data.map(group => ({
        id: `group_${group.ID}`,
        type: 'group',
        name: group.GroupName,
        avatar: group.GroupHeader || '',
        lastMessage: '暂无消息',
        lastTime: formatTime(group.CreatedAt),
        unread: 0,
        online: true,
        // 保存原始群组数据，用于后续聊天功能
        originalData: group
      }))

      console.log('转换后的群组数据:', groupConversations.value)

      // 如果当前是群聊标签页且有群组数据，自动选择第一个群组
      if (activeTab.value === 'groups' && groupConversations.value.length > 0) {
        selectedConversation.value = groupConversations.value[0]
        console.log('自动选择第一个群组:', selectedConversation.value)
      }
    } else {
      console.log('API 响应格式不正确或无数据:', responseData)
    }
  } catch (error) {
    console.error('获取群组列表失败:', error)
  }
}

// 格式化时间
const formatTime = (timeStr) => {
  const date = new Date(timeStr)
  const now = new Date()
  const today = new Date(now.getFullYear(), now.getMonth(), now.getDate())
  const messageDate = new Date(date.getFullYear(), date.getMonth(), date.getDate())

  if (messageDate.getTime() === today.getTime()) {
    // 今天的消息显示时间
    return date.toLocaleTimeString('zh-CN', { hour: '2-digit', minute: '2-digit' })
  } else if (messageDate.getTime() === today.getTime() - 24 * 60 * 60 * 1000) {
    // 昨天的消息
    return '昨天'
  } else {
    // 更早的消息显示日期
    return date.toLocaleDateString('zh-CN', { month: '2-digit', day: '2-digit' })
  }
}

// 组件挂载时获取群组列表
onMounted(() => {
  console.log('组件已挂载，开始获取群组列表')
  fetchGroupList()
})

// 监听用户变化
watch(() => props.user, (newUser) => {
  currentUser.value = newUser
}, { immediate: true })

// 监听标签页切换
watch(activeTab, (newTab) => {
  if (newTab === 'groups') {
    // 切换到群聊时，自动选择唯一的群聊
    if (groupConversations.value.length > 0) {
      selectedConversation.value = groupConversations.value[0]
    }
  } else if (newTab === 'friends') {
    // 切换到好友时，清空选择，显示空状态
    selectedConversation.value = null
  }
}, { immediate: true })

defineExpose({
  open: async (user) => {
    currentUser.value = user
    activeTab.value = 'groups' // 默认显示群聊
    visible.value = true
    await fetchGroupList()
  },
  close: closeDialog
})
</script>
<style>
.el-dialog.chat-dialog{
  height: 650px !important;
}
.el-dialog{
  border-radius: 20px;
}
.el-dialog__header{
  padding-bottom: 0 !important;
  padding: 0 !important;
}
.el-dialog__body{
  /* height: calc(100% - 60px) !important; */
  height: 100%;
  background: #1f2937;
}
.el-dialog.chat-dialog{
  height: 800px;
  padding: 0;
}
.el-empty {
  --el-empty-description-color: #9ca3af;
}
.el-empty__description p {
  color: #9ca3af !important;
}
</style>
<style lang="scss" scoped>
// 样式已移至 styles/chat.scss 文件中

.dialog-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: #1f2937;
  color: white;
  border-bottom: 1px solid #374151;
  border-radius: 10px 10px 0 0;

  .header-left {
    display: flex;
    align-items: center;
    gap: 10px;

    .username {
      font-weight: 500;
      font-size: 16px;
    }
  }

  .header-right {
    display: flex;
    align-items: center;
    gap: 15px;

    .chat-count {
      font-size: 14px;
      opacity: 0.8;
    }
  }
}

/* All layout styles moved to Tailwind CSS classes */
</style>
